package service

import (
	"context"
	"errors"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"inv-cloud-platform/fe-ev-rcpt-tmpl-api/internal/core/domain"
)

// MockTemplateStoreManager is a mock implementation of port.TemplateStoreManager
type MockTemplateStoreManager struct {
	mock.Mock
}

func (m *MockTemplateStoreManager) GetAllTemplates(ctx context.Context) (domain.Templates, error) {
	args := m.Called(ctx)
	if args.Get(0) != nil {
		return args.Get(0).(domain.Templates), args.Error(1)
	}
	return nil, args.Error(1)
}

func (m *MockTemplateStoreManager) GetTemplate(ctx context.Context, connectionID, templateID string) (*domain.Template, error) {
	args := m.Called(ctx, connectionID, templateID)
	if args.Get(0) != nil {
		return args.Get(0).(*domain.Template), args.Error(1)
	}
	return nil, args.Error(1)
}

func (m *MockTemplateStoreManager) GetTemplateByCountryAndParty(ctx context.Context, countryCode, partyCode string) (*domain.Template, error) {
	args := m.Called(ctx, countryCode, partyCode)
	if args.Get(0) != nil {
		return args.Get(0).(*domain.Template), args.Error(1)
	}
	return nil, args.Error(1)
}

func (m *MockTemplateStoreManager) SaveTemplate(ctx context.Context, template *domain.Template) error {
	args := m.Called(ctx, template)
	return args.Error(0)
}

func (m *MockTemplateStoreManager) UpdateTemplate(ctx context.Context, template *domain.Template) error {
	args := m.Called(ctx, template)
	return args.Error(0)
}

func (m *MockTemplateStoreManager) DeleteTemplate(ctx context.Context, connectionID, templateID string) error {
	args := m.Called(ctx, connectionID, templateID)
	return args.Error(0)
}

func TestNewTemplateSrv(t *testing.T) {
	mockRepo := &MockTemplateStoreManager{}
	service := NewTemplateSrv(mockRepo)

	assert.NotNil(t, service)
	assert.Equal(t, mockRepo, service.manager)
}

func TestTemplateSrv_ListTemplate(t *testing.T) {
	mockRepo := &MockTemplateStoreManager{}
	service := NewTemplateSrv(mockRepo)
	ctx := context.Background()

	expectedTemplates := domain.Templates{
		&domain.Template{
			TemplateID:   uuid.NewString(),
			ConnectionID: uuid.NewString(),
			Body:         "test body",
		},
	}

	t.Run("successful retrieval", func(t *testing.T) {
		mockRepo.On("GetAllTemplates", ctx).Return(expectedTemplates, nil).Once()

		result, err := service.ListTemplate(ctx)

		assert.NoError(t, err)
		assert.Equal(t, expectedTemplates, result)
		mockRepo.AssertExpectations(t)
	})

	t.Run("repository error", func(t *testing.T) {
		expectedError := errors.New("repository error")
		mockRepo.On("GetAllTemplates", ctx).Return(nil, expectedError).Once()

		result, err := service.ListTemplate(ctx)

		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, expectedError, err)
		mockRepo.AssertExpectations(t)
	})
}

func TestTemplateSrv_RetrieveTemplate(t *testing.T) {
	mockRepo := &MockTemplateStoreManager{}
	service := NewTemplateSrv(mockRepo)
	ctx := context.Background()

	templateID := uuid.NewString()
	connectionID := uuid.NewString()
	expectedTemplate := &domain.Template{
		TemplateID:   templateID,
		ConnectionID: connectionID,
		Body:         "test body",
	}

	t.Run("successful retrieval", func(t *testing.T) {
		mockRepo.On("GetTemplate", ctx, connectionID, templateID).Return(expectedTemplate, nil).Once()

		result, err := service.RetrieveTemplate(ctx, connectionID, templateID)

		assert.NoError(t, err)
		assert.Equal(t, expectedTemplate, result)
		mockRepo.AssertExpectations(t)
	})

	t.Run("template not found", func(t *testing.T) {
		mockRepo.On("GetTemplate", ctx, connectionID, templateID).Return(nil, domain.ErrRecordNotFound).Once()

		result, err := service.RetrieveTemplate(ctx, connectionID, templateID)

		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, domain.ErrRecordNotFound, err)
		mockRepo.AssertExpectations(t)
	})
}

func TestTemplateSrv_RetrieveTemplateByCountryAndParty(t *testing.T) {
	mockRepo := &MockTemplateStoreManager{}
	service := NewTemplateSrv(mockRepo)
	ctx := context.Background()

	countryCode := "US"
	partyCode := "ABC123"
	expectedTemplate := &domain.Template{
		TemplateID:   uuid.NewString(),
		ConnectionID: uuid.NewString(),
		Body:         "test body",
		Connection: domain.Connection{
			CountryCode: countryCode,
			PartyCode:   partyCode,
		},
	}

	t.Run("successful retrieval", func(t *testing.T) {
		mockRepo.On("GetTemplateByCountryAndParty", ctx, countryCode, partyCode).Return(expectedTemplate, nil).Once()

		result, err := service.RetrieveTemplateByCountryAndParty(ctx, countryCode, partyCode)

		assert.NoError(t, err)
		assert.Equal(t, expectedTemplate, result)
		mockRepo.AssertExpectations(t)
	})

	t.Run("template not found", func(t *testing.T) {
		mockRepo.On("GetTemplateByCountryAndParty", ctx, countryCode, partyCode).Return(nil, domain.ErrRecordNotFound).Once()

		result, err := service.RetrieveTemplateByCountryAndParty(ctx, countryCode, partyCode)

		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, domain.ErrRecordNotFound, err)
		mockRepo.AssertExpectations(t)
	})
}

func TestTemplateSrv_NewTemplate(t *testing.T) {
	mockRepo := &MockTemplateStoreManager{}
	service := NewTemplateSrv(mockRepo)
	ctx := context.Background()

	t.Run("successful creation with valid template", func(t *testing.T) {
		template := &domain.Template{
			ConnectionID: uuid.NewString(),
			Connection: domain.Connection{
				ConnectionName: "Test Connection",
				CountryCode:    "US",
				PartyCode:      "ABC123",
				CPOURL:         "https://example.com",
			},
		}

		mockRepo.On("SaveTemplate", ctx, mock.MatchedBy(func(t *domain.Template) bool {
			return t.ConnectionID == template.ConnectionID &&
				t.TemplateID != "" && // Should be generated
				t.Body == "<h4>Sample Body</h4>" && // Should be set by defaults
				t.HeaderText.Font == "times" // Should be set by defaults
		})).Return(nil).Once()

		err := service.NewTemplate(ctx, template)

		assert.NoError(t, err)
		assert.NotEmpty(t, template.TemplateID)                // Should be generated
		assert.Equal(t, "<h4>Sample Body</h4>", template.Body) // Should be set by defaults
		assert.Equal(t, "arial", template.HeaderText.Font)     // Should be set by defaults
		assert.False(t, template.LastUpdated.IsZero())         // Should be set
		mockRepo.AssertExpectations(t)
	})

	t.Run("validation error - missing connection ID", func(t *testing.T) {
		template := &domain.Template{
			// Missing ConnectionID
			Connection: domain.Connection{
				ConnectionName: "Test Connection",
				CountryCode:    "US",
				PartyCode:      "ABC123",
				CPOURL:         "https://example.com",
			},
		}

		err := service.NewTemplate(ctx, template)

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid connection ID")
		mockRepo.AssertNotCalled(t, "SaveTemplate")
	})

	t.Run("validation error - missing connection name", func(t *testing.T) {
		template := &domain.Template{
			ConnectionID: uuid.NewString(),
			Connection: domain.Connection{
				// Missing ConnectionName
				CountryCode: "US",
				PartyCode:   "ABC123",
				CPOURL:      "https://example.com",
			},
		}

		err := service.NewTemplate(ctx, template)

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid connection name")
		mockRepo.AssertNotCalled(t, "SaveTemplate")
	})

	t.Run("repository error", func(t *testing.T) {
		template := &domain.Template{
			ConnectionID: uuid.NewString(),
			Connection: domain.Connection{
				ConnectionName: "Test Connection",
				CountryCode:    "US",
				PartyCode:      "ABC123",
				CPOURL:         "https://example.com",
			},
		}

		expectedError := errors.New("repository error")
		mockRepo.On("SaveTemplate", ctx, mock.AnythingOfType("*domain.Template")).Return(expectedError).Once()

		err := service.NewTemplate(ctx, template)

		assert.Error(t, err)
		assert.Equal(t, expectedError, err)
		mockRepo.AssertExpectations(t)
	})
}
